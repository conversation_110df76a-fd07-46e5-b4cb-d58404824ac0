// 设备检测和下载链接配置
const DOWNLOAD_LINKS = {
    android: 'https://vip.123pan.cn/1837080827/24234092',
    ios: 'https://vip.123pan.cn/1837080827/24234093',
    webGame: 'https://h5.gzyuhu.com/user?token=eyJwcm9kdWN0Q29kZSI6Ijc1NmNkNTA4M2Q0ODRjMjM4ZmI0MWVmYjdkNmUzYzg3IiwiYXBwU2VjcmV0IjoiMGNkYTgwNzQ0MzRmNDhmMThkMmE5NDkxZmVkY2VmMjUiLCJjaGFubmVsQ29kZSI6Imt1Z3VhIiwicGxhdGZvcm0iOjN9'
};

// 福利兑换码配置
const WELFARE_CODES = [
    'VIP1314', 'HWHY666', 'VIP111', 'VIP222', 'VIP333', 'VIP666', 
    'VIP888', 'VIP999', 'HW588', 'VIP588', 'VIP599', 'VIP678', 
    'HWHW888', 'VIP669', '666666', '888888', '999999'
];

// 全局变量
let currentDevice = null;
let isLoading = true;

// 设备检测类
class DeviceDetector {
    constructor() {
        this.userAgent = navigator.userAgent.toLowerCase();
        this.platform = navigator.platform.toLowerCase();
        this.maxTouchPoints = navigator.maxTouchPoints || 0;
    }

    // 检测设备类型
    detectDevice() {
        // iOS 设备检测
        if (this.isIOS()) {
            return {
                type: 'ios',
                name: 'iOS设备',
                icon: '📱',
                downloadText: '立即下载APP',
                description: '适用于iPhone/iPad修仙'
            };
        }

        // Android 设备检测（包括鸿蒙）
        if (this.isAndroid() || this.isHarmonyOS()) {
            return {
                type: 'android',
                name: this.isHarmonyOS() ? '鸿蒙设备' : 'Android设备',
                icon: '📱',
                downloadText: '立即下载APP',
                description: '适用于Android/鸿蒙侠客'
            };
        }

        // PC 设备
        return {
            type: 'pc',
            name: 'PC设备',
            icon: '💻',
            downloadText: '进入江湖',
            description: '电脑端武侠体验'
        };
    }

    // iOS 检测
    isIOS() {
        return /iphone|ipad|ipod/.test(this.userAgent) || 
               (this.platform === 'macintel' && this.maxTouchPoints > 1);
    }

    // Android 检测
    isAndroid() {
        return /android/.test(this.userAgent);
    }

    // 鸿蒙系统检测
    isHarmonyOS() {
        return /harmonyos|openharmony/.test(this.userAgent) ||
               /huawei/.test(this.userAgent) && /mobile/.test(this.userAgent);
    }

    // 移动设备检测
    isMobile() {
        return this.isIOS() || this.isAndroid() || this.isHarmonyOS() ||
               /mobile|tablet|ipad|playbook|silk/i.test(this.userAgent);
    }

    // 平板检测
    isTablet() {
        return /tablet|ipad|playbook|silk/i.test(this.userAgent) ||
               (this.isAndroid() && !/mobile/i.test(this.userAgent));
    }

    // 微信环境检测
    isWeChat() {
        return /micromessenger/i.test(this.userAgent);
    }

    // 微信小程序检测
    isWeChatMiniProgram() {
        return /miniprogram/i.test(this.userAgent) || window.__wxjs_environment === 'miniprogram';
    }

    // QQ浏览器检测
    isQQ() {
        return /qq/i.test(this.userAgent) && !/qqbrowser/i.test(this.userAgent);
    }
}

// 下载管理器
class DownloadManager {
    constructor() {
        this.detector = new DeviceDetector();
    }

    // 执行下载
    download(deviceType) {
        // 检查是否在微信环境
        if (this.detector.isWeChat()) {
            this.handleWeChatDownload(deviceType);
            return;
        }

        let downloadUrl;
        let fileName;

        switch (deviceType) {
            case 'android':
                downloadUrl = DOWNLOAD_LINKS.android;
                fileName = '乾坤大挪移.apk';
                this.downloadFile(downloadUrl, fileName);
                this.showToast('正在下载乾坤大挪移...');
                break;

            case 'ios':
                downloadUrl = DOWNLOAD_LINKS.ios;
                fileName = '乾坤大挪移配置.mobileconfig';
                this.downloadFile(downloadUrl, fileName);
                this.showToast('正在下载乾坤大挪移配置...');
                break;

            case 'pc':
            default:
                this.openWebGame();
                this.showToast('正在进入乾坤大挪移武侠世界...');
                break;
        }

        // 统计下载事件
        this.trackDownload(deviceType);
    }

    // 处理微信环境下的下载
    handleWeChatDownload(deviceType) {
        let message;
        let action;

        switch (deviceType) {
            case 'android':
            case 'ios':
                message = '微信中无法直接下载，请在浏览器中打开';
                action = '尝试在新窗口打开下载链接';
                
                // 尝试在新窗口打开下载链接
                const downloadUrl = deviceType === 'android' ? DOWNLOAD_LINKS.android : DOWNLOAD_LINKS.ios;
                setTimeout(() => {
                    window.open(downloadUrl, '_blank');
                }, 1000);
                break;

            case 'pc':
            default:
                message = '正在打开网页版...';
                action = '网页版不受微信限制';
                this.openWebGame();
                break;
        }

        this.showToast(message);
        console.log(`微信环境处理: ${action}`);
        
        // 统计微信环境下的下载尝试
        this.trackDownload(deviceType + '_wechat');
    }

    // 下载文件
    downloadFile(url, fileName) {
        try {
            // 创建隐藏的下载链接
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;
            link.style.display = 'none';
            
            // 添加到DOM并触发下载
            document.body.appendChild(link);
            link.click();
            
            // 清理
            setTimeout(() => {
                document.body.removeChild(link);
            }, 100);

        } catch (error) {
            console.error('下载失败:', error);
            // 降级处理：直接打开链接
            window.open(url, '_blank');
        }
    }

    // 打开网页游戏
    openWebGame() {
        window.open(DOWNLOAD_LINKS.webGame, '_blank');
    }

    // 显示提示消息
    showToast(message) {
        const toast = document.getElementById('successToast');
        const messageElement = toast.querySelector('.toast-message');
        
        messageElement.textContent = message;
        toast.classList.add('show');

        // 3秒后自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }

    // 统计下载事件（可扩展）
    trackDownload(deviceType) {
        // 这里可以添加统计代码
        console.log(`下载统计: ${deviceType} 设备下载`);
        
        // 示例：发送统计数据到服务器
        // fetch('/api/stats/download', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify({ deviceType, timestamp: Date.now() })
        // }).catch(err => console.log('统计发送失败:', err));
    }
}

// UI管理器
class UIManager {
    constructor() {
        this.downloadManager = new DownloadManager();
        this.detector = new DeviceDetector();
        this.pendingAction = null; // 待执行的操作
    }

    // 初始化UI
    init() {
        this.updateDeviceInfo();
        this.bindEvents();
        this.checkWeChatEnvironment();
        this.hideLoading();
    }

    // 更新设备信息显示
    updateDeviceInfo() {
        currentDevice = this.detector.detectDevice();
        
        const deviceTypeElement = document.getElementById('deviceType');
        const downloadSubtitle = document.getElementById('downloadSubtitle');
        
        if (deviceTypeElement) {
            let deviceText = `检测到: ${currentDevice.name}`;
            
            // 添加微信环境标识
            if (this.detector.isWeChat()) {
                deviceText += ' (微信环境)';
            } else if (this.detector.isQQ()) {
                deviceText += ' (QQ环境)';
            }
            
            deviceTypeElement.textContent = deviceText;
        }
        
        if (downloadSubtitle) {
            downloadSubtitle.textContent = currentDevice.description;
        }

        // 更新按钮图标和文本
        this.updateDownloadButton();
    }

    // 更新下载按钮
    updateDownloadButton() {
        const downloadBtn = document.getElementById('downloadBtn');
        const btnIcon = downloadBtn.querySelector('.btn-icon');
        const btnText = downloadBtn.querySelector('.btn-text');
        
        if (btnIcon) {
            btnIcon.textContent = currentDevice.icon;
        }
        
        if (btnText) {
            btnText.textContent = currentDevice.downloadText;
        }
    }

    // 绑定事件
    bindEvents() {
        // 下载按钮点击事件
        const downloadBtn = document.getElementById('downloadBtn');
        if (downloadBtn) {
            downloadBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleDownload();
            });
        }

        // 网页直接玩按钮点击事件
        const webPlayBtn = document.getElementById('webPlayBtn');
        if (webPlayBtn) {
            webPlayBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleWebPlay();
            });
        }

        // 添加按钮点击动画
        this.addButtonAnimations();

        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            this.updateDeviceInfo();
        });

        // 监听设备方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.updateDeviceInfo();
            }, 100);
        });

        // 微信引导关闭按钮
        const continueBtn = document.getElementById('continueInWechat');
        if (continueBtn) {
            continueBtn.addEventListener('click', () => {
                this.hideWeChatGuide();
            });
        }

        // 友情提醒相关按钮
        const confirmReminderBtn = document.getElementById('confirmReminder');
        const copyWechatBtn = document.getElementById('copyWechatBtn');
        
        if (confirmReminderBtn) {
            confirmReminderBtn.addEventListener('click', () => {
                this.hideReminder();
            });
        }
        
        if (copyWechatBtn) {
            copyWechatBtn.addEventListener('click', () => {
                this.copyWechatNumber();
            });
        }

        // 福利兑换码相关按钮
        const welfareBtn = document.getElementById('welfareBtn');
        const closeWelfareBtn = document.getElementById('closeWelfare');
        const copyAllCodesBtn = document.getElementById('copyAllCodes');
        
        if (welfareBtn) {
            welfareBtn.addEventListener('click', () => {
                this.showWelfareModal();
            });
        }
        
        if (closeWelfareBtn) {
            closeWelfareBtn.addEventListener('click', () => {
                this.hideWelfareModal();
            });
        }
        
        if (copyAllCodesBtn) {
            copyAllCodesBtn.addEventListener('click', () => {
                this.copyAllCodes();
            });
        }

        // 点击遮罩关闭弹窗
        const welfareModal = document.getElementById('welfareModal');
        if (welfareModal) {
            welfareModal.addEventListener('click', (e) => {
                if (e.target === welfareModal) {
                    this.hideWelfareModal();
                }
            });
        }
    }

    // 处理下载
    handleDownload() {
        const btn = document.getElementById('downloadBtn');
        this.animateButton(btn);
        
        // 显示友情提醒，然后执行下载
        this.pendingAction = () => {
            this.downloadManager.download(currentDevice.type);
        };
        
        setTimeout(() => {
            this.showReminder();
        }, 200);
    }

    // 处理网页直接玩
    handleWebPlay() {
        const btn = document.getElementById('webPlayBtn');
        this.animateButton(btn);
        
        // 显示友情提醒，然后执行网页游戏
        this.pendingAction = () => {
            this.downloadManager.openWebGame();
            this.downloadManager.showToast('正在开启武侠之旅...');
        };
        
        setTimeout(() => {
            this.showReminder();
        }, 200);
    }

    // 按钮动画
    animateButton(button) {
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = '';
        }, 150);
    }

    // 添加按钮动画效果
    addButtonAnimations() {
        const buttons = document.querySelectorAll('.btn');
        
        buttons.forEach(button => {
            // 触摸反馈
            button.addEventListener('touchstart', () => {
                button.style.transform = 'scale(0.98)';
            });
            
            button.addEventListener('touchend', () => {
                setTimeout(() => {
                    button.style.transform = '';
                }, 100);
            });
            
            // 鼠标反馈
            button.addEventListener('mousedown', () => {
                button.style.transform = 'scale(0.98)';
            });
            
            button.addEventListener('mouseup', () => {
                setTimeout(() => {
                    button.style.transform = '';
                }, 100);
            });
        });
    }

    // 隐藏加载动画
    hideLoading() {
        setTimeout(() => {
            const loading = document.getElementById('loading');
            if (loading) {
                loading.classList.add('hidden');
                setTimeout(() => {
                    loading.style.display = 'none';
                }, 500);
            }
            isLoading = false;
        }, 1500); // 1.5秒后隐藏加载动画
    }

    // 检查微信环境
    checkWeChatEnvironment() {
        if (this.detector.isWeChat()) {
            // 检查用户是否已经选择过继续使用
            const hasSeenGuide = localStorage.getItem('wechat_guide_seen');
            const lastSeenTime = localStorage.getItem('wechat_guide_time');
            const now = Date.now();
            
            // 如果超过7天或者从未见过，则显示引导
            if (!hasSeenGuide || !lastSeenTime || (now - parseInt(lastSeenTime)) > 7 * 24 * 60 * 60 * 1000) {
                setTimeout(() => {
                    this.showWeChatGuide();
                }, 2000); // 延迟2秒显示，让页面先加载完成
            }
            
            // 修改按钮文本，提醒微信环境的限制
            this.updateButtonsForWeChat();
        }
    }

    // 显示微信引导
    showWeChatGuide() {
        const mask = document.getElementById('wechatMask');
        if (mask) {
            mask.classList.add('show');
            
            // 禁止背景滚动
            document.body.style.overflow = 'hidden';
            
            // 统计显示次数
            this.trackWeChatGuideShow();
        }
    }

    // 隐藏微信引导
    hideWeChatGuide() {
        const mask = document.getElementById('wechatMask');
        if (mask) {
            mask.classList.remove('show');
            
            // 恢复背景滚动
            document.body.style.overflow = '';
            
            // 记录用户已经看过引导
            localStorage.setItem('wechat_guide_seen', 'true');
            localStorage.setItem('wechat_guide_time', Date.now().toString());
            
            // 显示提示
            this.downloadManager.showToast('继续在微信中体验，部分功能可能受限');
            
            // 统计用户选择
            this.trackWeChatGuideAction('continue');
        }
    }

    // 为微信环境更新按钮
    updateButtonsForWeChat() {
        const downloadBtn = document.getElementById('downloadBtn');
        const webPlayBtn = document.getElementById('webPlayBtn');
        
        if (downloadBtn) {
            const subtitle = downloadBtn.querySelector('.btn-subtitle');
            if (subtitle) {
                subtitle.textContent = '微信环境，建议浏览器打开';
            }
        }
        
        if (webPlayBtn) {
            const subtitle = webPlayBtn.querySelector('.btn-subtitle');
            if (subtitle) {
                subtitle.textContent = '推荐：更好的游戏体验';
            }
        }
    }

    // 统计微信引导显示
    trackWeChatGuideShow() {
        console.log('微信引导已显示');
        // 这里可以添加统计代码
        // fetch('/api/stats/wechat-guide-show', { method: 'POST' })
    }

    // 统计微信引导用户行为
    trackWeChatGuideAction(action) {
        console.log(`微信引导用户行为: ${action}`);
        // 这里可以添加统计代码
        // fetch('/api/stats/wechat-guide-action', { 
        //     method: 'POST',
        //     body: JSON.stringify({ action })
        // })
    }

    // 显示友情提醒
    showReminder() {
        const reminder = document.getElementById('friendlyReminder');
        if (reminder) {
            reminder.classList.add('show');
            
            // 禁止背景滚动
            document.body.style.overflow = 'hidden';
            
            // 统计提醒显示
            this.trackReminderShow();
        }
    }

    // 隐藏友情提醒
    hideReminder() {
        const reminder = document.getElementById('friendlyReminder');
        if (reminder) {
            reminder.classList.remove('show');
            
            // 恢复背景滚动
            document.body.style.overflow = '';
            
            // 执行待执行的操作
            if (this.pendingAction) {
                setTimeout(() => {
                    this.pendingAction();
                    this.pendingAction = null;
                }, 300);
            }
            
            // 统计用户行为
            this.trackReminderAction('confirmed');
        }
    }

    // 复制微信号
    copyWechatNumber() {
        const wechatNumber = '658967';
        const copyBtn = document.getElementById('copyWechatBtn');
        
        // 使用现代剪贴板API
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(wechatNumber).then(() => {
                this.showCopySuccess(copyBtn);
                this.downloadManager.showToast('瓜瓜微信号已复制：' + wechatNumber);
            }).catch(err => {
                console.error('复制失败:', err);
                this.fallbackCopyToClipboard(wechatNumber, copyBtn);
            });
        } else {
            // 降级处理
            this.fallbackCopyToClipboard(wechatNumber, copyBtn);
        }
        
        // 统计复制操作
        this.trackReminderAction('copy_wechat');
    }

    // 降级复制方法
    fallbackCopyToClipboard(text, button) {
        try {
            // 创建临时文本框
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            
            textArea.focus();
            textArea.select();
            
            // 执行复制命令
            const successful = document.execCommand('copy');
            document.body.removeChild(textArea);
            
            if (successful) {
                this.showCopySuccess(button);
                this.downloadManager.showToast('瓜瓜微信号已复制：' + text);
            } else {
                this.downloadManager.showToast('复制失败，请手动复制瓜瓜微信号：' + text);
            }
        } catch (err) {
            console.error('降级复制失败:', err);
            this.downloadManager.showToast('请手动复制瓜瓜微信号：' + text);
        }
    }

    // 显示复制成功效果
    showCopySuccess(button) {
        const originalText = button.textContent;
        
        button.classList.add('copied');
        button.textContent = '✅ 已复制';
        
        setTimeout(() => {
            button.classList.remove('copied');
            button.textContent = originalText;
        }, 1500);
    }

    // 统计提醒显示
    trackReminderShow() {
        console.log('友情提醒已显示');
        // 这里可以添加统计代码
        // fetch('/api/stats/reminder-show', { method: 'POST' })
    }

    // 统计提醒用户行为
    trackReminderAction(action) {
        console.log(`友情提醒用户行为: ${action}`);
        // 这里可以添加统计代码
        // fetch('/api/stats/reminder-action', { 
        //     method: 'POST',
        //     body: JSON.stringify({ action })
        // })
    }

    // 显示福利兑换码弹窗
    showWelfareModal() {
        const modal = document.getElementById('welfareModal');
        if (modal) {
            // 生成兑换码列表
            this.generateCodesList();
            
            modal.classList.add('show');
            
            // 禁止背景滚动
            document.body.style.overflow = 'hidden';
            
            // 统计福利弹窗显示
            this.trackWelfareAction('show');
        }
    }

    // 隐藏福利兑换码弹窗
    hideWelfareModal() {
        const modal = document.getElementById('welfareModal');
        if (modal) {
            modal.classList.remove('show');
            
            // 恢复背景滚动
            document.body.style.overflow = '';
            
            // 统计福利弹窗关闭
            this.trackWelfareAction('close');
        }
    }

    // 生成兑换码列表
    generateCodesList() {
        const codesGrid = document.getElementById('codesGrid');
        if (!codesGrid) return;

        // 清空现有内容
        codesGrid.innerHTML = '';

        // 为每个兑换码创建DOM元素
        WELFARE_CODES.forEach((code, index) => {
            const codeItem = document.createElement('div');
            codeItem.className = 'code-item';
            codeItem.innerHTML = `
                <span class="code-text">${code}</span>
                <button class="code-copy-btn" data-code="${code}">复制</button>
            `;

            // 为每个复制按钮添加事件监听
            const copyBtn = codeItem.querySelector('.code-copy-btn');
            copyBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.copySingleCode(code, copyBtn, codeItem);
            });

            // 点击整个item也可以复制
            codeItem.addEventListener('click', () => {
                this.copySingleCode(code, copyBtn, codeItem);
            });

            codesGrid.appendChild(codeItem);
        });
    }

    // 复制单个兑换码
    copySingleCode(code, button, item) {
        this.copyToClipboard(code).then(() => {
            // 显示复制成功效果
            this.showCodeCopySuccess(button, item);
            
            // 显示Toast提示
            this.downloadManager.showToast(`已复制兑换码：${code}`);
            
            // 统计复制操作
            this.trackWelfareAction('copy_single', code);
        }).catch(err => {
            console.error('复制失败:', err);
            this.downloadManager.showToast(`复制失败，请手动复制：${code}`);
        });
    }

    // 复制所有兑换码
    copyAllCodes() {
        const allCodes = WELFARE_CODES.join('\n');
        
        this.copyToClipboard(allCodes).then(() => {
            // 显示复制成功效果
            this.showAllCodesCopySuccess();
            
            // 显示Toast提示
            this.downloadManager.showToast(`已复制全部 ${WELFARE_CODES.length} 个兑换码`);
            
            // 统计复制操作
            this.trackWelfareAction('copy_all');
        }).catch(err => {
            console.error('复制全部失败:', err);
            this.downloadManager.showToast('复制失败，请逐个复制兑换码');
        });
    }

    // 通用复制到剪贴板方法
    copyToClipboard(text) {
        return new Promise((resolve, reject) => {
            // 使用现代剪贴板API
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(resolve).catch(reject);
            } else {
                // 降级处理
                this.fallbackCopyToClipboard(text).then(resolve).catch(reject);
            }
        });
    }

    // 降级复制方法（Promise版本）
    fallbackCopyToClipboard(text) {
        return new Promise((resolve, reject) => {
            try {
                // 创建临时文本框
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                
                textArea.focus();
                textArea.select();
                
                // 执行复制命令
                const successful = document.execCommand('copy');
                document.body.removeChild(textArea);
                
                if (successful) {
                    resolve();
                } else {
                    reject(new Error('execCommand 复制失败'));
                }
            } catch (err) {
                reject(err);
            }
        });
    }

    // 显示单个兑换码复制成功效果
    showCodeCopySuccess(button, item) {
        const originalText = button.textContent;
        
        // 按钮变色和文字
        button.classList.add('copied');
        button.textContent = '✅';
        
        // 整个item变色
        item.classList.add('copied');
        
        // 1.5秒后恢复
        setTimeout(() => {
            button.classList.remove('copied');
            button.textContent = originalText;
            item.classList.remove('copied');
        }, 1500);
    }

    // 显示全部兑换码复制成功效果
    showAllCodesCopySuccess() {
        const copyAllBtn = document.getElementById('copyAllCodes');
        const originalText = copyAllBtn.textContent;
        
        copyAllBtn.textContent = '✅ 全部已复制';
        copyAllBtn.style.background = 'linear-gradient(135deg, #27ae60, #2ecc71)';
        
        // 所有code item都显示复制效果
        const codeItems = document.querySelectorAll('.code-item');
        const copyBtns = document.querySelectorAll('.code-copy-btn');
        
        codeItems.forEach(item => item.classList.add('copied'));
        copyBtns.forEach(btn => {
            btn.classList.add('copied');
            btn.textContent = '✅';
        });
        
        // 2秒后恢复
        setTimeout(() => {
            copyAllBtn.textContent = originalText;
            copyAllBtn.style.background = '';
            
            codeItems.forEach(item => item.classList.remove('copied'));
            copyBtns.forEach(btn => {
                btn.classList.remove('copied');
                btn.textContent = '复制';
            });
        }, 2000);
    }

    // 统计福利相关操作
    trackWelfareAction(action, extra = '') {
        console.log(`福利兑换码操作: ${action}`, extra);
        // 这里可以添加统计代码
        // fetch('/api/stats/welfare-action', { 
        //     method: 'POST',
        //     body: JSON.stringify({ action, extra })
        // })
    }
}

// 性能监控
class PerformanceMonitor {
    constructor() {
        this.startTime = performance.now();
        this.metrics = {
            pageLoad: 0,
            deviceDetection: 0,
            uiRender: 0
        };
    }

    // 记录性能指标
    recordMetric(name, value) {
        this.metrics[name] = value;
        console.log(`性能指标 ${name}: ${value.toFixed(2)}ms`);
    }

    // 获取页面加载时间
    getPageLoadTime() {
        return performance.now() - this.startTime;
    }

    // 监控资源加载
    monitorResources() {
        window.addEventListener('load', () => {
            const navigation = performance.getEntriesByType('navigation')[0];
            console.log('页面加载性能:', {
                domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                totalTime: navigation.loadEventEnd - navigation.fetchStart
            });
        });
    }
}

// 错误处理
class ErrorHandler {
    constructor() {
        this.setupGlobalErrorHandling();
    }

    // 设置全局错误处理
    setupGlobalErrorHandling() {
        window.addEventListener('error', (event) => {
            this.logError('JavaScript Error', event.error);
        });

        window.addEventListener('unhandledrejection', (event) => {
            this.logError('Promise Rejection', event.reason);
        });
    }

    // 记录错误
    logError(type, error) {
        console.error(`${type}:`, error);
        
        // 这里可以添加错误上报逻辑
        // this.reportError(type, error);
    }

    // 错误上报（示例）
    reportError(type, error) {
        // fetch('/api/errors', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify({
        //         type,
        //         message: error.message,
        //         stack: error.stack,
        //         userAgent: navigator.userAgent,
        //         timestamp: Date.now()
        //     })
        // }).catch(err => console.log('错误上报失败:', err));
    }
}

// 主应用程序
class App {
    constructor() {
        this.uiManager = new UIManager();
        this.performanceMonitor = new PerformanceMonitor();
        this.errorHandler = new ErrorHandler();
    }

    // 初始化应用
    init() {
        console.log('应用初始化开始...');
        
        const startTime = performance.now();
        
        // 设备检测
        const deviceStartTime = performance.now();
        this.uiManager.updateDeviceInfo();
        this.performanceMonitor.recordMetric('deviceDetection', performance.now() - deviceStartTime);
        
        // UI初始化
        const uiStartTime = performance.now();
        this.uiManager.init();
        this.performanceMonitor.recordMetric('uiRender', performance.now() - uiStartTime);
        
        // 性能监控
        this.performanceMonitor.monitorResources();
        
        console.log('应用初始化完成');
        this.performanceMonitor.recordMetric('pageLoad', performance.now() - startTime);
    }
}

// DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    const app = new App();
    app.init();
});

// 页面可见性变化处理
document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
        console.log('页面重新获得焦点');
        // 可以在这里重新检测设备或刷新状态
    }
});

// 导出供其他脚本使用（如果需要）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        DeviceDetector,
        DownloadManager,
        UIManager,
        App
    };
} 
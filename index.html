<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>乾坤大挪移 - 智能下载中心</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 加载动画 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>正在加载...</p>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
        <header class="header">
            <h1 class="title">乾坤大挪移</h1>
            <p class="subtitle">多平台智能下载，随时随地畅玩武侠世界</p>
        </header>

        <main class="main-content">
            <div class="buttons-container">
                <!-- 立即下载按钮 -->
                <button id="downloadBtn" class="btn btn-primary">
                    <span class="btn-icon">📱</span>
                    <span class="btn-text">立即下载</span>
                    <span class="btn-subtitle" id="downloadSubtitle">智能识别设备类型</span>
                </button>

                <!-- 网页直接玩按钮 -->
                <button id="webPlayBtn" class="btn btn-secondary">
                    <span class="btn-icon">🎮</span>
                    <span class="btn-text">网页直接玩</span>
                    <span class="btn-subtitle">无需下载，即刻开始</span>
                </button>
            </div>

            <!-- 设备信息显示 -->
            <div class="device-info">
                <span id="deviceType">正在检测设备类型...</span>
            </div>

            <!-- 福利兑换码按钮 -->
            <div class="welfare-section">
                <button id="welfareBtn" class="btn btn-welfare">
                    <span class="btn-icon">🎁</span>
                    <span class="btn-text">福利兑换码</span>
                    <span class="btn-subtitle">免费领取游戏福利</span>
                </button>
            </div>

            <!-- 特性介绍 -->
            <div class="features">
                <div class="feature-item">
                    <div class="feature-icon">⚡</div>
                    <div class="feature-text">
                        <h3>武功秘籍</h3>
                        <p>掌握乾坤大挪移神功，纵横江湖</p>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🗺️</div>
                    <div class="feature-text">
                        <h3>江湖闯荡</h3>
                        <p>多平台同步，随时随地体验武侠人生</p>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">⚔️</div>
                    <div class="feature-text">
                        <h3>侠客之路</h3>
                        <p>即刻开启，无需等待的武侠冒险</p>
                    </div>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2025 乾坤大挪移. 为您提供最佳武侠游戏体验</p>
        </footer>
    </div>

    <!-- 微信浏览器提醒 -->
    <div id="wechatMask" class="wechat-mask">
        <div class="wechat-guide">
            <div class="guide-header">
                <h3>🔍 检测到微信环境</h3>
                <p>为了更好的游戏体验，请在浏览器中打开</p>
            </div>
            <div class="guide-content">
                <div class="guide-step">
                    <div class="step-number">1</div>
                    <div class="step-text">点击右上角 <strong>⋯</strong> 菜单</div>
                </div>
                <div class="guide-step">
                    <div class="step-number">2</div>
                    <div class="step-text">选择 <strong>"在浏览器中打开"</strong></div>
                </div>
                <div class="guide-image">
                    <div class="phone-mockup">
                        <div class="phone-header">
                            <span class="phone-title">乾坤大挪移</span>
                            <span class="phone-menu">⋯</span>
                        </div>
                        <div class="menu-dropdown">
                            <div class="menu-item highlighted">📱 在浏览器中打开</div>
                            <div class="menu-item">🔗 复制链接</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="guide-footer">
                <button id="continueInWechat" class="btn-continue">
                    <span>我知道了，继续体验</span>
                </button>
                <p class="guide-note">⚠️ 在微信中可能无法正常下载文件</p>
            </div>
        </div>
    </div>

    <!-- 福利兑换码弹窗 -->
    <div id="welfareModal" class="welfare-modal">
        <div class="welfare-content">
            <div class="welfare-header">
                <span class="welfare-icon">🎁</span>
                <h3>福利兑换码</h3>
                <p>复制兑换码到游戏中即可领取丰厚奖励</p>
                <button class="close-btn" id="closeWelfare">×</button>
            </div>
            <div class="welfare-body">
                <div class="codes-grid" id="codesGrid">
                    <!-- 兑换码将通过JavaScript动态生成 -->
                </div>
            </div>
            <div class="welfare-footer">
                <button class="btn-copy-all" id="copyAllCodes">📋 复制全部</button>
                <p class="welfare-tip">💡 建议收藏本页面，随时领取福利</p>
            </div>
        </div>
    </div>

    <!-- 友情提醒弹窗 -->
    <div id="friendlyReminder" class="reminder-modal">
        <div class="reminder-content">
            <div class="reminder-header">
                <span class="reminder-icon">🎁</span>
                <h3>专属福利提醒</h3>
            </div>
            <div class="reminder-body">
                <p class="reminder-title">🎉 注册成功后，记得联系瓜瓜：</p>
                <div class="benefits-list">
                    <div class="benefit-item">
                        <span class="benefit-icon">🎁</span>
                        <span class="benefit-text">新手专属礼包</span>
                    </div>
                    <div class="benefit-item">
                        <span class="benefit-icon">💎</span>
                        <span class="benefit-text">换区奖励补偿</span>
                    </div>
                    <div class="benefit-item">
                        <span class="benefit-icon">⚡</span>
                        <span class="benefit-text">游戏问题快速解决</span>
                    </div>
                    <div class="benefit-item">
                        <span class="benefit-icon">🏆</span>
                        <span class="benefit-text">VIP特权激活</span>
                    </div>
                </div>
                <div class="wechat-info">
                    <div class="wechat-simple">
                        <div class="wechat-text">
                            <span class="pointing-hand">👉</span>
                            <span class="wechat-label">瓜瓜微信号：</span>
                            <span class="wechat-number" id="wechatNumber">658967</span>
                        </div>
                        <button class="copy-btn" id="copyWechatBtn">复制微信号</button>
                    </div>
                </div>
                <p class="reminder-note">💡 这是瓜瓜的微信号，不是游戏验证码哦～</p>
            </div>
            <div class="reminder-footer">
                <button class="btn-confirm" id="confirmReminder">知道了，继续游戏</button>
            </div>
        </div>
    </div>

    <!-- 成功提示 -->
    <div id="successToast" class="toast">
        <span class="toast-icon">✅</span>
        <span class="toast-message">操作成功！</span>
    </div>

    <script src="script.js"></script>
</body>
</html> 
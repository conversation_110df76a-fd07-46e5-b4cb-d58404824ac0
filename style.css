/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.3)), url('1.png');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    background-repeat: no-repeat;
    min-height: 100vh;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease;
}

.loading-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    font-size: 16px;
    font-weight: 500;
}

/* 主容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 60px;
    color: white;
}

.title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease;
}

.subtitle {
    font-size: 1.2rem;
    font-weight: 400;
    opacity: 0.9;
    animation: fadeInUp 1s ease 0.2s both;
}

/* 主要内容 */
.main-content {
    width: 100%;
    max-width: 600px;
    text-align: center;
}

/* 按钮容器 */
.buttons-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 40px;
}

/* 按钮样式 */
.btn {
    position: relative;
    width: 100%;
    padding: 25px 35px;
    border: none;
    border-radius: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    animation: fadeInUp 1s ease 0.4s both;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.btn:active {
    transform: translateY(-1px);
}

/* 主要按钮 */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.6);
}

/* 次要按钮 */
.btn-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    box-shadow: 0 8px 20px rgba(240, 147, 251, 0.4);
}

.btn-secondary:hover {
    box-shadow: 0 12px 30px rgba(240, 147, 251, 0.6);
}

.btn-icon {
    font-size: 1.5rem;
}

.btn-text {
    font-size: 1.2rem;
    font-weight: 600;
}

.btn-subtitle {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 400;
}

/* 设备信息 */
.device-info {
    display: none; /* 根据用户需求隐藏此元素 */
    margin-bottom: 30px;
    padding: 15px 25px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 25px;
    color: white;
    font-weight: 500;
    animation: fadeInUp 1s ease 0.6s both;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 福利兑换码区域 */
.welfare-section {
    margin-bottom: 40px;
    animation: fadeInUp 1s ease 0.7s both;
}

/* 福利按钮 */
.btn-welfare {
    background: linear-gradient(135deg, #ff9a56 0%, #ff6b95 100%);
    color: white;
    box-shadow: 0 8px 20px rgba(255, 154, 86, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.btn-welfare:hover {
    box-shadow: 0 12px 30px rgba(255, 154, 86, 0.6);
    transform: translateY(-3px);
}

.btn-welfare::after {
    content: '🌟';
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 1.2rem;
    animation: sparkle 2s infinite;
}

@keyframes sparkle {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.2) rotate(180deg);
        opacity: 1;
    }
}

/* 特性介绍 */
.features {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    margin-bottom: 40px;
    animation: fadeInUp 1s ease 0.8s both;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-2px);
}

.feature-icon {
    font-size: 2rem;
    min-width: 60px;
    text-align: center;
}

.feature-text {
    text-align: left;
    color: white;
}

.feature-text h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.feature-text p {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* 页脚 */
.footer {
    margin-top: auto;
    padding: 30px 0;
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    animation: fadeInUp 1s ease 1s both;
}

/* 提示消息 */
.toast {
    position: fixed;
    top: 30px;
    right: 30px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    padding: 15px 25px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.toast.show {
    transform: translateX(0);
}

.toast-icon {
    font-size: 1.2rem;
}

.toast-message {
    font-weight: 500;
}

/* 动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .title {
        font-size: 2.5rem;
    }
    
    .subtitle {
        font-size: 1rem;
    }
    
    .btn {
        padding: 20px 25px;
        font-size: 1rem;
    }
    
    .btn-text {
        font-size: 1.1rem;
    }
    
    .btn-subtitle {
        font-size: 0.8rem;
    }
    
    .feature-item {
        flex-direction: column;
        text-align: center;
    }
    
    .feature-text {
        text-align: center;
    }
    
    .toast {
        right: 15px;
        left: 15px;
        transform: translateY(-100px);
    }
    
    .toast.show {
        transform: translateY(0);
    }
}

@media (max-width: 480px) {
    .title {
        font-size: 2rem;
    }
    
    .btn {
        padding: 18px 20px;
    }
    
    .header {
        margin-bottom: 40px;
    }
    
    .device-info {
        font-size: 0.9rem;
    }
    
    .feature-item {
        padding: 15px;
    }
}

/* 高分辨率显示器优化 */
@media (min-width: 1200px) {
    .title {
        font-size: 4rem;
    }
    
    .subtitle {
        font-size: 1.4rem;
    }
    
    .buttons-container {
        flex-direction: row;
        gap: 30px;
    }
    
    .btn {
        max-width: 280px;
    }
    
    .features {
        grid-template-columns: repeat(3, 1fr);
        gap: 30px;
    }
}

/* 微信浏览器提醒 */
.wechat-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    z-index: 10000;
    display: none;
    justify-content: center;
    align-items: center;
    padding: 20px;
    animation: fadeIn 0.5s ease;
}

.wechat-mask.show {
    display: flex;
}

.wechat-guide {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: 20px;
    padding: 30px;
    max-width: 400px;
    width: 100%;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.5s ease;
    color: #333;
}

.guide-header h3 {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #2c3e50;
}

.guide-header p {
    font-size: 1rem;
    color: #7f8c8d;
    margin-bottom: 25px;
}

.guide-content {
    margin-bottom: 25px;
}

.guide-step {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    text-align: left;
}

.step-number {
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    margin-right: 15px;
    flex-shrink: 0;
}

.step-text {
    font-size: 1rem;
    color: #2c3e50;
}

.step-text strong {
    color: #e74c3c;
    font-weight: 600;
}

.guide-image {
    margin: 25px 0;
    display: flex;
    justify-content: center;
}

.phone-mockup {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    border-radius: 15px;
    padding: 15px;
    position: relative;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.phone-header {
    background: #3498db;
    color: white;
    padding: 12px 15px;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.phone-title {
    font-weight: 600;
}

.phone-menu {
    font-size: 1.2rem;
    animation: pulse 2s infinite;
}

.menu-dropdown {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.menu-item {
    padding: 12px 15px;
    font-size: 0.9rem;
    border-bottom: 1px solid #ecf0f1;
    transition: all 0.3s ease;
}

.menu-item:last-child {
    border-bottom: none;
}

.menu-item.highlighted {
    background: linear-gradient(135deg, #e8f5e8, #d4edda);
    color: #27ae60;
    font-weight: 600;
    animation: highlight 2s infinite;
}

.btn-continue {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

.btn-continue:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.btn-continue:active {
    transform: translateY(0);
}

.guide-note {
    font-size: 0.85rem;
    color: #e67e22;
    margin: 0;
    font-weight: 500;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes highlight {
    0%, 100% {
        background: linear-gradient(135deg, #e8f5e8, #d4edda);
    }
    50% {
        background: linear-gradient(135deg, #d4edda, #c3e6cb);
    }
}

/* 微信提醒响应式设计 */
@media (max-width: 480px) {
    .wechat-guide {
        padding: 20px;
        margin: 10px;
    }
    
    .guide-header h3 {
        font-size: 1.2rem;
    }
    
    .guide-header p {
        font-size: 0.9rem;
    }
    
    .step-text {
        font-size: 0.9rem;
    }
    
    .phone-mockup {
        padding: 10px;
    }
    
    .phone-header {
        padding: 10px 12px;
        font-size: 0.8rem;
    }
    
    .menu-item {
        padding: 10px 12px;
        font-size: 0.8rem;
    }
}

/* 友情提醒弹窗 */
.reminder-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
    z-index: 10001;
    display: none;
    justify-content: center;
    align-items: center;
    padding: 20px;
    animation: fadeIn 0.3s ease;
}

.reminder-modal.show {
    display: flex;
}

.reminder-content {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: 15px;
    padding: 25px;
    max-width: 380px;
    width: 100%;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
    animation: bounceIn 0.4s ease;
    color: #333;
    position: relative;
}

.reminder-header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.reminder-icon {
    font-size: 2rem;
    display: block;
    margin-bottom: 8px;
}

.reminder-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.reminder-body {
    margin-bottom: 25px;
}

.reminder-body p {
    font-size: 1rem;
    color: #34495e;
    text-align: center;
    margin-bottom: 15px;
    line-height: 1.5;
}

.reminder-title {
    font-weight: 600;
    color: #2c3e50 !important;
    margin-bottom: 20px !important;
}

.benefits-list {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #dee2e6;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.benefit-item:last-child {
    border-bottom: none;
}

.benefit-icon {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
}

.benefit-text {
    font-size: 0.95rem;
    color: #495057;
    font-weight: 500;
}

.reminder-note {
    font-size: 0.85rem !important;
    color: #6c757d !important;
    font-style: italic;
    margin-top: 10px !important;
    margin-bottom: 0 !important;
}

.wechat-info {
    margin-top: 10px;
}

.wechat-simple {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.wechat-text {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.pointing-hand {
    font-size: 1.2rem;
    animation: point 1.5s ease-in-out infinite;
}

.wechat-label {
    font-size: 1.1rem;
    color: #2c3e50;
    font-weight: 500;
}

.wechat-number {
    font-size: 1.3rem;
    font-weight: 700;
    color: #07c160;
    background: #e8f5e8;
    padding: 8px 16px;
    border-radius: 8px;
    letter-spacing: 2px;
    user-select: all;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid #07c160;
}

.wechat-number:hover {
    background: #d4edda;
    transform: scale(1.05);
}

.copy-btn {
    background: #07c160;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.copy-btn:hover {
    background: #00ae56;
}

/* 手势指向动画 */
@keyframes point {
    0%, 100% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(3px);
    }
}

.copy-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
}

.copy-btn:active {
    transform: translateY(0);
}

.copy-btn.copied {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    animation: copySuccess 0.6s ease;
}

.reminder-footer {
    text-align: center;
}

.btn-confirm {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 140px;
}

.btn-confirm:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(102, 126, 234, 0.4);
}

.btn-confirm:active {
    transform: translateY(0);
}

/* 动画效果 */
@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3) translateY(-50px);
    }
    50% {
        opacity: 1;
        transform: scale(1.05) translateY(0);
    }
    70% {
        transform: scale(0.95);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes copySuccess {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}



/* 友情提醒响应式设计 */
@media (max-width: 480px) {
    .reminder-content {
        padding: 20px;
        margin: 10px;
    }
    
    .reminder-header h3 {
        font-size: 1.2rem;
    }
    
    .reminder-body p {
        font-size: 0.95rem;
    }

    .benefits-list {
        padding: 15px;
        margin-bottom: 15px;
    }

    .benefit-item {
        padding: 6px 0;
    }

    .benefit-icon {
        font-size: 1.1rem;
        width: 20px;
    }

    .benefit-text {
        font-size: 0.9rem;
    }

    .reminder-note {
        font-size: 0.8rem !important;
    }
    
    .wechat-simple {
        padding: 16px;
    }

    .wechat-text {
        flex-direction: column;
        gap: 12px;
    }

    .wechat-label {
        font-size: 1rem;
    }

    .wechat-number {
        font-size: 1.2rem;
        padding: 6px 12px;
        letter-spacing: 1px;
    }

    .copy-btn {
        padding: 8px 16px;
        font-size: 0.85rem;
    }
    
    .btn-confirm {
        padding: 12px 20px;
        font-size: 0.95rem;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .btn:hover {
        transform: none;
    }
    
    .btn:active {
        transform: scale(0.95);
    }
    
    .feature-item:hover {
        transform: none;
    }
    
    .btn-continue:hover {
        transform: none;
    }
    
    .btn-continue:active {
        transform: scale(0.95);
    }
    
    .copy-btn:hover {
        transform: none;
    }
    
    .copy-btn:active {
        transform: scale(0.95);
    }
    
    .btn-confirm:hover {
        transform: none;
    }
    
    .btn-confirm:active {
        transform: scale(0.95);
    }
    
    .wechat-number:hover {
        transform: none;
    }
    
    .btn-welfare:hover {
        transform: none;
    }
    
    .btn-welfare:active {
        transform: scale(0.95);
    }
    
    .code-copy-btn:hover {
        transform: none;
    }
    
    .code-copy-btn:active {
        transform: scale(0.95);
    }
    
    .btn-copy-all:hover {
        transform: none;
    }
    
    .btn-copy-all:active {
        transform: scale(0.95);
    }
    
    .close-btn:hover {
        transform: none;
    }
    
    .close-btn:active {
        transform: scale(0.95);
    }
    
    .code-item:hover {
        transform: none;
    }
}

/* 福利兑换码弹窗 */
.welfare-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 10002;
    display: none;
    justify-content: center;
    align-items: center;
    padding: 20px;
    animation: fadeIn 0.3s ease;
}

.welfare-modal.show {
    display: flex;
}

.welfare-content {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: 20px;
    padding: 30px;
    max-width: 500px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: zoomIn 0.4s ease;
    color: #333;
    position: relative;
}

.welfare-header {
    text-align: center;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
    position: relative;
}

.welfare-icon {
    font-size: 2.5rem;
    display: block;
    margin-bottom: 10px;
    animation: bounce 2s infinite;
}

.welfare-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.welfare-header p {
    font-size: 0.95rem;
    color: #7f8c8d;
    margin: 0;
}

.close-btn {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
}

.close-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 12px rgba(231, 76, 60, 0.5);
}

.welfare-body {
    margin-bottom: 25px;
}

.codes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    max-height: 300px;
    overflow-y: auto;
    padding: 5px;
}

.code-item {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px solid #dee2e6;
    border-radius: 10px;
    padding: 12px 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.code-item:hover {
    border-color: #ff9a56;
    background: linear-gradient(135deg, #fff5f0, #ffe8d6);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 154, 86, 0.2);
}

.code-item.copied {
    border-color: #27ae60;
    background: linear-gradient(135deg, #e8f5e8, #d4edda);
    animation: copyPulse 0.6s ease;
}

.code-text {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    letter-spacing: 1px;
    user-select: all;
}

.code-copy-btn {
    background: linear-gradient(135deg, #ff9a56, #ff6b95);
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    min-width: 50px;
}

.code-copy-btn:hover {
    transform: scale(1.05);
}

.code-copy-btn.copied {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.welfare-footer {
    text-align: center;
    border-top: 2px solid #e9ecef;
    padding-top: 20px;
}

.btn-copy-all {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

.btn-copy-all:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(102, 126, 234, 0.4);
}

.btn-copy-all:active {
    transform: translateY(0);
}

.welfare-tip {
    font-size: 0.85rem;
    color: #7f8c8d;
    margin: 0;
    font-style: italic;
}

/* 福利兑换码动画效果 */
@keyframes zoomIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes copyPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
}

/* 福利兑换码响应式设计 */
@media (max-width: 480px) {
    .welfare-content {
        padding: 20px;
        margin: 10px;
        max-height: 85vh;
    }
    
    .welfare-header h3 {
        font-size: 1.3rem;
    }
    
    .welfare-header p {
        font-size: 0.9rem;
    }
    
    .codes-grid {
        grid-template-columns: 1fr;
        max-height: 250px;
    }
    
    .code-item {
        padding: 10px 12px;
    }
    
    .code-text {
        font-size: 0.95rem;
    }
    
    .code-copy-btn {
        padding: 6px 10px;
        font-size: 0.75rem;
        min-width: 45px;
    }
    
    .btn-copy-all {
        padding: 10px 25px;
        font-size: 0.9rem;
    }
    
    .welfare-tip {
        font-size: 0.8rem;
    }
} 